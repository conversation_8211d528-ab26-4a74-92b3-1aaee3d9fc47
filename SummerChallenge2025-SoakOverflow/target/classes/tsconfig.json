{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "checkJs": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react", "lib": ["dom", "dom.iterable", "esnext"], "module": "es2015", "moduleResolution": "node", "noEmit": false, "noImplicitAny": false, "resolveJsonModule": false, "skipLibCheck": true, "sourceMap": false, "target": "es6", "outDir": "view", "types": ["pixi.js"]}, "include": ["ts"]}