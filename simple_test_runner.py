#!/usr/bin/env python3
"""
Simple test runner for the SoakOverflow bot
Tests both MCTS and heuristic modes with sample inputs
"""

import subprocess
import time
import os
from datetime import datetime

def create_test_scenarios():
    """Create different test scenarios"""
    scenarios = {
        "basic_combat": """0
2
0 0 3 2 10 1
1 1 3 2 10 1
10 10
0 0 0
1 0 0
2 0 0
3 0 0
4 0 0
5 0 0
6 0 0
7 0 0
8 0 0
9 0 0
0 1 0
1 1 0
2 1 0
3 1 0
4 1 0
5 1 0
6 1 0
7 1 0
8 1 0
9 1 0
0 2 0
1 2 0
2 2 0
3 2 0
4 2 0
5 2 0
6 2 0
7 2 0
8 2 0
9 2 0
0 3 0
1 3 0
2 3 0
3 3 0
4 3 0
5 3 0
6 3 0
7 3 0
8 3 0
9 3 0
0 4 0
1 4 0
2 4 0
3 4 0
4 4 0
5 4 0
6 4 0
7 4 0
8 4 0
9 4 0
0 5 0
1 5 0
2 5 0
3 5 0
4 5 0
5 5 0
6 5 0
7 5 0
8 5 0
9 5 0
0 6 0
1 6 0
2 6 0
3 6 0
4 6 0
5 6 0
6 6 0
7 6 0
8 6 0
9 6 0
0 7 0
1 7 0
2 7 0
3 7 0
4 7 0
5 7 0
6 7 0
7 7 0
8 7 0
9 7 0
0 8 0
1 8 0
2 8 0
3 8 0
4 8 0
5 8 0
6 8 0
7 8 0
8 8 0
9 8 0
0 9 0
1 9 0
2 9 0
3 9 0
4 9 0
5 9 0
6 9 0
7 9 0
8 9 0
9 9 0
2
0 1 1 0 1 0
1 8 8 0 1 0
2""",
        
        "close_range": """0
2
0 0 2 1 15 2
1 1 2 1 15 2
5 5
0 0 0
1 0 0
2 0 0
3 0 0
4 0 0
0 1 0
1 1 0
2 1 0
3 1 0
4 1 0
0 2 0
1 2 0
2 2 0
3 2 0
4 2 0
0 3 0
1 3 0
2 3 0
3 3 0
4 3 0
0 4 0
1 4 0
2 4 0
3 4 0
4 4 0
2
0 1 1 0 2 0
1 3 3 0 2 0
2"""
    }
    return scenarios

def test_bot_mode(mode, scenario_name, scenario_input, timeout=5):
    """Test the bot with a specific mode and scenario"""
    print(f"Testing {mode} mode with {scenario_name} scenario...")
    
    # Write scenario to temp file
    with open(f"temp_scenario_{scenario_name}.txt", "w") as f:
        f.write(scenario_input)
    
    try:
        # Run bot with timeout
        cmd = ["timeout", f"{timeout}s", "./bot", mode] if mode != "mcts" else ["timeout", f"{timeout}s", "./bot"]
        
        result = subprocess.run(
            cmd,
            input=scenario_input,
            text=True,
            capture_output=True,
            timeout=timeout + 1
        )
        
        # Analyze output
        actions_taken = []
        decision_quality = 0
        
        if result.stderr:
            # Parse debug output
            lines = result.stderr.split('\n')
            for line in lines:
                if "chose" in line and "action" in line:
                    actions_taken.append(line.strip())
                if "MCTS" in line:
                    decision_quality += 2  # MCTS gets higher quality score
                elif "heuristic" in line:
                    decision_quality += 1
        
        if result.stdout:
            # Parse actual actions
            output_actions = result.stdout.strip().split('\n')
            valid_actions = [a for a in output_actions if ';' in a]
            
        success = result.returncode in [0, 124]  # 0 = success, 124 = timeout (expected)
        
        return {
            "mode": mode,
            "scenario": scenario_name,
            "success": success,
            "actions_taken": len(actions_taken),
            "decision_quality": decision_quality,
            "output_lines": len(result.stdout.split('\n')) if result.stdout else 0,
            "stderr_lines": len(result.stderr.split('\n')) if result.stderr else 0,
            "return_code": result.returncode
        }
        
    except Exception as e:
        return {
            "mode": mode,
            "scenario": scenario_name,
            "success": False,
            "error": str(e),
            "actions_taken": 0,
            "decision_quality": 0
        }
    finally:
        # Clean up temp file
        if os.path.exists(f"temp_scenario_{scenario_name}.txt"):
            os.remove(f"temp_scenario_{scenario_name}.txt")

def run_comprehensive_test():
    """Run comprehensive tests on both modes"""
    print("SoakOverflow Bot Comprehensive Test")
    print("=" * 40)
    
    # Ensure bot is compiled
    print("Compiling bot...")
    compile_result = subprocess.run(["g++", "-o", "bot", "bot.cpp", "-std=c++14"], 
                                  capture_output=True, text=True)
    if compile_result.returncode != 0:
        print(f"Compilation failed: {compile_result.stderr}")
        return
    
    scenarios = create_test_scenarios()
    modes = ["mcts", "heuristic"]
    results = []
    
    for mode in modes:
        print(f"\n--- Testing {mode.upper()} mode ---")
        for scenario_name, scenario_input in scenarios.items():
            result = test_bot_mode(mode, scenario_name, scenario_input)
            results.append(result)
            
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{scenario_name}: {status} (Actions: {result['actions_taken']}, Quality: {result['decision_quality']})")
            
            if "error" in result:
                print(f"  Error: {result['error']}")
    
    # Summary analysis
    print(f"\n--- SUMMARY ---")
    mcts_results = [r for r in results if r["mode"] == "mcts"]
    heuristic_results = [r for r in results if r["mode"] == "heuristic"]
    
    mcts_success_rate = sum(1 for r in mcts_results if r["success"]) / len(mcts_results)
    heuristic_success_rate = sum(1 for r in heuristic_results if r["success"]) / len(heuristic_results)
    
    mcts_avg_quality = sum(r["decision_quality"] for r in mcts_results) / len(mcts_results)
    heuristic_avg_quality = sum(r["decision_quality"] for r in heuristic_results) / len(heuristic_results)
    
    print(f"MCTS Mode:")
    print(f"  Success Rate: {mcts_success_rate:.1%}")
    print(f"  Avg Decision Quality: {mcts_avg_quality:.1f}")
    
    print(f"Heuristic Mode:")
    print(f"  Success Rate: {heuristic_success_rate:.1%}")
    print(f"  Avg Decision Quality: {heuristic_avg_quality:.1f}")
    
    # Recommendations
    print(f"\n--- RECOMMENDATIONS ---")
    if mcts_success_rate > heuristic_success_rate:
        print("• MCTS mode shows better success rate - use for competitive play")
    else:
        print("• Heuristic mode shows better success rate - MCTS may need tuning")
        
    if mcts_avg_quality > heuristic_avg_quality:
        print("• MCTS shows higher decision quality")
    else:
        print("• Heuristic shows higher decision quality - consider improving MCTS")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    with open(f"test_results_{timestamp}.txt", "w") as f:
        f.write("SoakOverflow Bot Test Results\n")
        f.write("=" * 30 + "\n\n")
        for result in results:
            f.write(f"{result}\n")
    
    print(f"\nDetailed results saved to test_results_{timestamp}.txt")

if __name__ == "__main__":
    run_comprehensive_test()
