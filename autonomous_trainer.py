#!/usr/bin/env python3
"""
Autonomous Training System for SoakOverflow Bot
Runs multiple games, analyzes performance, and suggests improvements.
"""

import subprocess
import json
import time
import os
import re
from datetime import datetime
from typing import Dict, List, Tuple
import statistics

class GameResult:
    def __init__(self, winner: int, scores: Dict[int, int], duration: int, log_data: str):
        self.winner = winner
        self.scores = scores
        self.duration = duration
        self.log_data = log_data
        self.timestamp = datetime.now()

class PerformanceAnalyzer:
    def __init__(self):
        self.results = []
        
    def add_result(self, result: GameResult):
        self.results.append(result)
        
    def analyze_performance(self) -> Dict:
        if not self.results:
            return {"error": "No results to analyze"}
            
        total_games = len(self.results)
        wins = sum(1 for r in self.results if r.winner == 0)  # Assuming our bot is player 0
        win_rate = wins / total_games
        
        avg_duration = statistics.mean(r.duration for r in self.results)
        
        # Analyze decision patterns from logs
        decision_patterns = self._analyze_decisions()
        
        return {
            "total_games": total_games,
            "wins": wins,
            "losses": total_games - wins,
            "win_rate": win_rate,
            "avg_duration": avg_duration,
            "decision_patterns": decision_patterns,
            "recommendations": self._generate_recommendations(win_rate, decision_patterns)
        }
        
    def _analyze_decisions(self) -> Dict:
        move_count = 0
        shoot_count = 0
        throw_count = 0
        hunker_count = 0
        
        for result in self.results:
            # Parse log data for action patterns
            moves = len(re.findall(r'MOVE \d+ \d+', result.log_data))
            shoots = len(re.findall(r'SHOOT \d+', result.log_data))
            throws = len(re.findall(r'THROW \d+ \d+', result.log_data))
            hunkers = len(re.findall(r'HUNKER_DOWN', result.log_data))
            
            move_count += moves
            shoot_count += shoots
            throw_count += throws
            hunker_count += hunkers
            
        total_actions = move_count + shoot_count + throw_count + hunker_count
        
        if total_actions == 0:
            return {"error": "No actions found in logs"}
            
        return {
            "move_percentage": move_count / total_actions * 100,
            "shoot_percentage": shoot_count / total_actions * 100,
            "throw_percentage": throw_count / total_actions * 100,
            "hunker_percentage": hunker_count / total_actions * 100,
            "total_actions": total_actions
        }
        
    def _generate_recommendations(self, win_rate: float, patterns: Dict) -> List[str]:
        recommendations = []
        
        if win_rate < 0.4:
            recommendations.append("Low win rate detected. Consider more aggressive strategies.")
            
        if win_rate > 0.8:
            recommendations.append("High win rate! Current strategy is working well.")
            
        if "move_percentage" in patterns:
            if patterns["move_percentage"] > 60:
                recommendations.append("High movement ratio. Consider more combat actions.")
            elif patterns["move_percentage"] < 20:
                recommendations.append("Low movement ratio. Consider better positioning.")
                
            if patterns["shoot_percentage"] < 10:
                recommendations.append("Very low shooting frequency. Increase aggression.")
                
            if patterns["hunker_percentage"] > 30:
                recommendations.append("Too much hunkering. Be more proactive.")
                
        return recommendations

class AutonomousTrainer:
    def __init__(self, bot_path: str = "./bot", engine_dir: str = "./SummerChallenge2025-SoakOverflow"):
        self.bot_path = bot_path
        self.engine_dir = engine_dir
        self.analyzer = PerformanceAnalyzer()
        
    def run_single_game(self, mode: str = "mcts") -> GameResult:
        """Run a single game and return the result"""
        print(f"Running game with {mode} mode...")
        
        # Start the game engine
        engine_cmd = ["mvn", "exec:java", "-Dexec.mainClass=Main", "-Dexec.classpathScope=test"]
        
        try:
            # Run the game engine (this will timeout after a reasonable time)
            result = subprocess.run(
                engine_cmd,
                cwd=self.engine_dir,
                capture_output=True,
                text=True,
                timeout=60  # 60 second timeout
            )
            
            # Parse the output to determine winner and scores
            # This is a simplified parser - in a real implementation, 
            # you'd parse the actual game output format
            winner = 0 if "Agent Alpha" in result.stdout else 1
            scores = {0: 100, 1: 50}  # Placeholder scores
            duration = 30  # Placeholder duration
            
            return GameResult(winner, scores, duration, result.stderr)
            
        except subprocess.TimeoutExpired:
            print("Game timed out")
            return GameResult(-1, {}, 60, "Game timed out")
        except Exception as e:
            print(f"Error running game: {e}")
            return GameResult(-1, {}, 0, str(e))
            
    def run_training_session(self, num_games: int = 10, modes: List[str] = ["mcts", "heuristic"]):
        """Run multiple games and analyze performance"""
        print(f"Starting training session with {num_games} games...")
        
        for i in range(num_games):
            mode = modes[i % len(modes)]
            print(f"\nGame {i+1}/{num_games} (Mode: {mode})")
            
            # Recompile bot with the specified mode
            self._prepare_bot(mode)
            
            result = self.run_single_game(mode)
            self.analyzer.add_result(result)
            
            # Print immediate feedback
            if result.winner == 0:
                print("✅ Won!")
            elif result.winner == 1:
                print("❌ Lost")
            else:
                print("⚠️ Error/Timeout")
                
        # Analyze overall performance
        analysis = self.analyzer.analyze_performance()
        self._print_analysis(analysis)
        self._save_analysis(analysis)
        
    def _prepare_bot(self, mode: str):
        """Recompile the bot for the specified mode"""
        compile_cmd = ["g++", "-o", "bot", "bot.cpp", "-std=c++14"]
        subprocess.run(compile_cmd, cwd=".", check=True)
        
    def _print_analysis(self, analysis: Dict):
        """Print the performance analysis"""
        print("\n" + "="*50)
        print("PERFORMANCE ANALYSIS")
        print("="*50)
        
        if "error" in analysis:
            print(f"Error: {analysis['error']}")
            return
            
        print(f"Total Games: {analysis['total_games']}")
        print(f"Wins: {analysis['wins']}")
        print(f"Losses: {analysis['losses']}")
        print(f"Win Rate: {analysis['win_rate']:.2%}")
        print(f"Average Duration: {analysis['avg_duration']:.1f}s")
        
        if "decision_patterns" in analysis and "error" not in analysis["decision_patterns"]:
            patterns = analysis["decision_patterns"]
            print(f"\nDecision Patterns:")
            print(f"  Move: {patterns['move_percentage']:.1f}%")
            print(f"  Shoot: {patterns['shoot_percentage']:.1f}%")
            print(f"  Throw: {patterns['throw_percentage']:.1f}%")
            print(f"  Hunker: {patterns['hunker_percentage']:.1f}%")
            
        print(f"\nRecommendations:")
        for rec in analysis["recommendations"]:
            print(f"  • {rec}")
            
    def _save_analysis(self, analysis: Dict):
        """Save analysis to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"training_analysis_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
            
        print(f"\nAnalysis saved to {filename}")

def main():
    trainer = AutonomousTrainer()
    
    print("SoakOverflow Autonomous Training System")
    print("=====================================")
    
    # Run a training session
    trainer.run_training_session(num_games=5, modes=["mcts", "heuristic"])
    
    print("\nTraining session complete!")

if __name__ == "__main__":
    main()
