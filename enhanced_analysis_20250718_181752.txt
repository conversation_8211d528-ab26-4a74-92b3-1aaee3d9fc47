SoakOverflow Enhanced Bot Analysis
========================================

{'mode': 'mcts', 'scenario': 'cover_mechanics', 'success': True, 'actions': ['Agent 0 chose MCTS action: MOVE 1 2', 'Agent 1 chose MCTS action: MOVE 3 2'], 'decision_reasoning': [], 'damage_calculations': 0, 'cover_considerations': 0, 'splash_evaluations': 0, 'final_actions': ['0;MOVE 1 2', '1;MOVE 3 2']}

{'mode': 'mcts', 'scenario': 'splash_bomb_scenario', 'success': True, 'actions': ['Agent 0 chose MCTS action: MOVE 1 2', 'Agent 1 chose MCTS action: MOVE 2 2'], 'decision_reasoning': [], 'damage_calculations': 0, 'cover_considerations': 0, 'splash_evaluations': 0, 'final_actions': ['0;MOVE 1 2', '1;MOVE 2 2']}

{'mode': 'heuristic', 'scenario': 'cover_mechanics', 'success': True, 'actions': ['Agent 0 chose heuristic action with score 40', 'Agent 1 chose heuristic action with score 20'], 'decision_reasoning': ['Agent 0 chose heuristic action with score 40', 'Agent 1 chose heuristic action with score 20'], 'damage_calculations': 0, 'cover_considerations': 0, 'splash_evaluations': 0, 'final_actions': ['0;MOVE 1 2', '1;MOVE 2 1']}

{'mode': 'heuristic', 'scenario': 'splash_bomb_scenario', 'success': True, 'actions': ['Agent 0 chose heuristic action with score 10', 'Agent 1 chose heuristic action with score 10'], 'decision_reasoning': ['Agent 0 chose heuristic action with score 10', 'Agent 1 chose heuristic action with score 10'], 'damage_calculations': 0, 'cover_considerations': 0, 'splash_evaluations': 0, 'final_actions': ['0;HUNKER_DOWN', '1;HUNKER_DOWN']}

