#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <map>
#include <cmath>
#include <sstream>
#include <climits>
#include <random>
#include <chrono>
#include <memory>

using namespace std;

/**
 * Win the water fight by controlling the most territory, or out-soak your opponent!
 **/

// Coordinate structure
struct Coord {
    int x, y;
    Coord(int x = 0, int y = 0) : x(x), y(y) {}

    int manhattanDistance(const Coord& other) const {
        return abs(x - other.x) + abs(y - other.y);
    }

    bool operator==(const Coord& other) const {
        return x == other.x && y == other.y;
    }
};

// Agent class data (static info) - Based on actual game source
struct AgentClass {
    int agent_id;
    int player_id;
    int shoot_cooldown;
    int optimal_range;
    int soaking_power;
    int max_splash_bombs;

    // Agent class types from source code:
    // GUNNER(1, 16, 4, 1) - Fast, medium damage, medium range
    // SNIPER(5, 24, 6, 0) - Slow, high damage, long range, no bombs
    // BOMBER(2, 8, 2, 3) - Medium speed, low damage, short range, many bombs
    // ASSAULT(2, 16, 4, 2) - Medium speed, medium damage, medium range, some bombs
    // BERSERKER(5, 32, 2, 1) - Slow, very high damage, short range
};

// Current agent state
struct Agent {
    int agent_id;
    Coord position;
    int cooldown;
    int splash_bombs;
    int wetness;
    int player_id; // Added to track which player owns this agent

    bool isAlive() const {
        return wetness < 100;
    }

    bool canShoot() const {
        return cooldown == 0 && isAlive();
    }

    bool canThrow() const {
        return splash_bombs > 0 && isAlive();
    }
};

// Tile types - Based on actual game source
enum TileType {
    TYPE_FLOOR = 0,        // No cover
    TYPE_LOW_COVER = 1,    // 0.5x damage modifier
    TYPE_HIGH_COVER = 2    // 0.25x damage modifier
};

// Game constants from source code
const int THROW_DAMAGE = 30;
const int THROW_DISTANCE_MAX = 4;
const int DEATH_THRESHOLD = 100;

// Game state structure
struct GameState {
    int my_id;
    int width, height;
    vector<vector<int>> grid; // tile types
    map<int, AgentClass> agent_classes;
    map<int, Agent> agents;
    vector<int> my_agent_ids;
    int current_my_agent_count; // Number of my agents that need actions this turn

    void initializeGrid(int w, int h) {
        width = w;
        height = h;
        grid = vector<vector<int>>(height, vector<int>(width, TYPE_FLOOR));
    }

    void setTile(int x, int y, int tile_type) {
        if (x >= 0 && x < width && y >= 0 && y < height) {
            grid[y][x] = tile_type;
        }
    }

    int getTile(int x, int y) const {
        if (x >= 0 && x < width && y >= 0 && y < height) {
            return grid[y][x];
        }
        return -1; // Out of bounds
    }

    bool isValidPosition(const Coord& pos) const {
        return pos.x >= 0 && pos.x < width && pos.y >= 0 && pos.y < height;
    }

    bool isPositionOccupied(const Coord& pos) const {
        for (const auto& pair : agents) {
            const Agent& agent = pair.second;
            if (agent.isAlive() && agent.position == pos) {
                return true;
            }
        }
        return false;
    }

    bool canMoveTo(const Coord& pos) const {
        return isValidPosition(pos) && !isPositionOccupied(pos);
    }

    double getCoverModifier(int x, int y) const {
        int tile = getTile(x, y);
        if (tile == TYPE_LOW_COVER) return 0.5;
        if (tile == TYPE_HIGH_COVER) return 0.25;
        return 1.0; // No cover
    }

    // Calculate damage based on game source code
    int calculateDamage(const Agent& shooter, const Agent& target) const {
        // Range modifier
        double distance = shooter.position.manhattanDistance(target.position);
        double rangeModifier = 0.0;

        if (agent_classes.count(shooter.agent_id)) {
            int optimalRange = agent_classes.at(shooter.agent_id).optimal_range;
            if (distance <= optimalRange) {
                rangeModifier = 1.0;
            } else if (distance <= optimalRange * 2) {
                rangeModifier = 0.5;
            }
        }

        if (rangeModifier == 0.0) return 0; // Out of range

        // Cover modifier (simplified - actual game has complex line-of-sight)
        double coverModifier = getCoverModifier(target.position.x, target.position.y);

        // Hunker bonus (25% damage reduction)
        double hunkerBonus = 0.0; // We don't track hunker state in our simplified model

        // Calculate final damage
        int soakingPower = agent_classes.count(shooter.agent_id) ?
                          agent_classes.at(shooter.agent_id).soaking_power : 10;

        return (int)(soakingPower * rangeModifier * (coverModifier - hunkerBonus));
    }

    vector<Agent> getMyAgents() const {
        vector<Agent> my_agents;
        for (int id : my_agent_ids) {
            if (agents.count(id) && agents.at(id).isAlive()) {
                my_agents.push_back(agents.at(id));
            }
        }
        return my_agents;
    }

    vector<Agent> getEnemyAgents() const {
        vector<Agent> enemy_agents;
        for (const auto& pair : agents) {
            const Agent& agent = pair.second;
            if (agent.player_id != my_id && agent.isAlive()) {
                enemy_agents.push_back(agent);
            }
        }
        return enemy_agents;
    }
};

// Action types
enum ActionType {
    MOVE,
    SHOOT,
    THROW,
    HUNKER_DOWN,
    MESSAGE
};

struct Action {
    ActionType type;
    Coord target;
    int target_id;
    string message;

    Action(ActionType t) : type(t), target_id(-1) {}
    Action(ActionType t, Coord pos) : type(t), target(pos), target_id(-1) {}
    Action(ActionType t, int id) : type(t), target_id(id) {}
    Action(ActionType t, string msg) : type(t), message(msg), target_id(-1) {}

    string toString() const {
        switch (type) {
            case MOVE:
                return "MOVE " + to_string(target.x) + " " + to_string(target.y);
            case SHOOT:
                return "SHOOT " + to_string(target_id);
            case THROW:
                return "THROW " + to_string(target.x) + " " + to_string(target.y);
            case HUNKER_DOWN:
                return "HUNKER_DOWN";
            case MESSAGE:
                return "MESSAGE " + message;
        }
        return "HUNKER_DOWN";
    }
};

// MCTS Node for decision making
struct MCTSNode {
    GameState state;
    vector<Action> available_actions;
    int agent_id;

    int visits;
    double total_score;
    MCTSNode* parent;
    vector<unique_ptr<MCTSNode>> children;

    MCTSNode(const GameState& game_state, int agent_id, MCTSNode* parent = nullptr)
        : state(game_state), agent_id(agent_id), visits(0), total_score(0.0), parent(parent) {}

    bool isFullyExpanded() const {
        return children.size() == available_actions.size();
    }

    bool isLeaf() const {
        return children.empty();
    }

    double getUCB1Score(double exploration_constant = sqrt(2)) const {
        if (visits == 0) return INFINITY;
        if (parent == nullptr || parent->visits == 0) return total_score / visits;

        double exploitation = total_score / visits;
        double exploration = exploration_constant * sqrt(log(parent->visits) / visits);
        return exploitation + exploration;
    }

    MCTSNode* selectBestChild() const {
        MCTSNode* best = nullptr;
        double best_score = -INFINITY;

        for (const auto& child : children) {
            double score = child->getUCB1Score();
            if (score > best_score) {
                best_score = score;
                best = child.get();
            }
        }
        return best;
    }
};

// Monte Carlo Tree Search implementation
class MCTS {
private:
    random_device rd;
    mt19937 gen;

public:
    MCTS() : gen(rd()) {}

    Action search(const GameState& initial_state, int agent_id, int max_iterations = 1000) {
        auto root = make_unique<MCTSNode>(initial_state, agent_id);

        // Generate available actions for the agent
        if (initial_state.agents.count(agent_id)) {
            const Agent& agent = initial_state.agents.at(agent_id);
            vector<Agent> enemies = initial_state.getEnemyAgents();
            root->available_actions = generateAllPossibleActions(agent, enemies, initial_state);
        }

        if (root->available_actions.empty()) {
            return Action(HUNKER_DOWN);
        }

        for (int i = 0; i < max_iterations; i++) {
            MCTSNode* leaf = select(root.get());
            MCTSNode* expanded = expand(leaf);
            double score = simulate(expanded);
            backpropagate(expanded, score);
        }

        // Return the action leading to the most visited child
        MCTSNode* best_child = nullptr;
        int max_visits = -1;

        for (const auto& child : root->children) {
            if (child->visits > max_visits) {
                max_visits = child->visits;
                best_child = child.get();
            }
        }

        if (best_child && !root->available_actions.empty()) {
            // Find which action led to this child
            for (size_t i = 0; i < root->children.size(); i++) {
                if (root->children[i].get() == best_child) {
                    return root->available_actions[i];
                }
            }
        }

        return root->available_actions[0];
    }

private:
    vector<Action> generateAllPossibleActions(const Agent& agent, const vector<Agent>& enemies, const GameState& state) {
        vector<Action> possible_actions;

        // Generate MOVE actions - check for agent collisions
        vector<Coord> directions = {{0, 1}, {0, -1}, {1, 0}, {-1, 0}};
        for (const Coord& dir : directions) {
            Coord target(agent.position.x + dir.x, agent.position.y + dir.y);
            if (state.canMoveTo(target)) {
                possible_actions.push_back(Action(MOVE, target));
            }
        }

        // Generate SHOOT actions
        if (agent.canShoot()) {
            for (const Agent& enemy : enemies) {
                possible_actions.push_back(Action(SHOOT, enemy.agent_id));
            }
        }

        // Generate THROW actions
        if (agent.canThrow()) {
            for (int dx = -2; dx <= 2; dx++) {
                for (int dy = -2; dy <= 2; dy++) {
                    if (abs(dx) + abs(dy) <= 2) {
                        Coord target(agent.position.x + dx, agent.position.y + dy);
                        if (state.isValidPosition(target)) {
                            possible_actions.push_back(Action(THROW, target));
                        }
                    }
                }
            }
        }

        possible_actions.push_back(Action(HUNKER_DOWN));
        return possible_actions;
    }

    MCTSNode* select(MCTSNode* node) {
        while (!node->isLeaf() && node->isFullyExpanded()) {
            node = node->selectBestChild();
        }
        return node;
    }

    MCTSNode* expand(MCTSNode* node) {
        if (node->available_actions.empty() || node->isFullyExpanded()) {
            return node;
        }

        // Add a new child for an unexplored action
        size_t action_index = node->children.size();
        if (action_index < node->available_actions.size()) {
            GameState new_state = node->state; // Copy state
            // Apply the action to create new state (simplified)
            auto new_child = make_unique<MCTSNode>(new_state, node->agent_id, node);
            MCTSNode* child_ptr = new_child.get();
            node->children.push_back(move(new_child));
            return child_ptr;
        }

        return node;
    }

    double simulate(MCTSNode* node) {
        // Simple random simulation
        GameState sim_state = node->state;

        // Evaluate current position
        double score = 0.0;

        if (sim_state.agents.count(node->agent_id)) {
            const Agent& agent = sim_state.agents.at(node->agent_id);
            vector<Agent> enemies = sim_state.getEnemyAgents();

            // Score based on agent health and position
            score += (100 - agent.wetness);

            // Score based on enemy proximity and threat
            for (const Agent& enemy : enemies) {
                int distance = agent.position.manhattanDistance(enemy.position);
                score += max(0, 10 - distance); // Closer enemies are more threatening
                score += enemy.wetness; // Prefer when enemies are more damaged
            }
        }

        return score;
    }

    void backpropagate(MCTSNode* node, double score) {
        while (node != nullptr) {
            node->visits++;
            node->total_score += score;
            node = node->parent;
        }
    }
};

// Game state parser and manager
class SoakOverflowBot {
private:
    GameState game_state;
    MCTS mcts;
    bool use_mcts;

public:
    SoakOverflowBot(bool enable_mcts = true) : use_mcts(enable_mcts) {}
    void parseInitialInput() {
        // Read player ID
        cin >> game_state.my_id;
        cin.ignore();

        // Read agent class data
        int agent_data_count;
        cin >> agent_data_count;
        cin.ignore();

        for (int i = 0; i < agent_data_count; i++) {
            AgentClass agent_class;
            cin >> agent_class.agent_id >> agent_class.player_id
                >> agent_class.shoot_cooldown >> agent_class.optimal_range
                >> agent_class.soaking_power >> agent_class.max_splash_bombs;
            cin.ignore();

            game_state.agent_classes[agent_class.agent_id] = agent_class;

            // Track our agent IDs
            if (agent_class.player_id == game_state.my_id) {
                game_state.my_agent_ids.push_back(agent_class.agent_id);
            }
        }

        // Read map dimensions
        cin >> game_state.width >> game_state.height;
        cin.ignore();
        game_state.initializeGrid(game_state.width, game_state.height);

        // Read map tiles
        for (int i = 0; i < game_state.height; i++) {
            for (int j = 0; j < game_state.width; j++) {
                int x, y, tile_type;
                cin >> x >> y >> tile_type;
                cin.ignore();
                game_state.setTile(x, y, tile_type);
            }
        }

        cerr << "Initialized game: Player " << game_state.my_id
             << ", Map " << game_state.width << "x" << game_state.height
             << ", My agents: " << game_state.my_agent_ids.size() << endl;
    }

    void parseGameTurn() {
        // Clear previous turn data
        game_state.agents.clear();

        // Read current agent states
        int agent_count;
        cin >> agent_count;
        cin.ignore();

        for (int i = 0; i < agent_count; i++) {
            Agent agent;
            cin >> agent.agent_id >> agent.position.x >> agent.position.y
                >> agent.cooldown >> agent.splash_bombs >> agent.wetness;
            cin.ignore();

            // Set player ID from agent class data
            if (game_state.agent_classes.count(agent.agent_id)) {
                agent.player_id = game_state.agent_classes[agent.agent_id].player_id;
            }

            game_state.agents[agent.agent_id] = agent;
        }

        // Read my agent count - this is crucial!
        int my_agent_count;
        cin >> my_agent_count;
        cin.ignore();

        // Store this for action generation
        game_state.current_my_agent_count = my_agent_count;
    }

    vector<Action> generateActions() {
        vector<Action> actions;
        vector<Agent> my_agents = game_state.getMyAgents();
        vector<Agent> enemy_agents = game_state.getEnemyAgents();

        cerr << "Turn: My agents: " << my_agents.size()
             << ", Enemy agents: " << enemy_agents.size()
             << ", Expected actions: " << game_state.current_my_agent_count << endl;

        // Generate actions for the number of agents the game expects
        for (int i = 0; i < game_state.current_my_agent_count && i < my_agents.size(); i++) {
            const Agent& agent = my_agents[i];
            Action action = chooseAction(agent, enemy_agents);
            actions.push_back(action);

            cerr << "Agent " << agent.agent_id << " at (" << agent.position.x
                 << "," << agent.position.y << ") -> " << action.toString() << endl;
        }

        return actions;
    }

    vector<Action> generateAllPossibleActions(const Agent& agent, const vector<Agent>& enemies) {
        vector<Action> possible_actions;

        // Generate MOVE actions - check for agent collisions
        vector<Coord> directions = {{0, 1}, {0, -1}, {1, 0}, {-1, 0}};
        for (const Coord& dir : directions) {
            Coord target(agent.position.x + dir.x, agent.position.y + dir.y);
            if (game_state.canMoveTo(target)) {
                possible_actions.push_back(Action(MOVE, target));
            }
        }

        // Generate SHOOT actions
        if (agent.canShoot()) {
            for (const Agent& enemy : enemies) {
                possible_actions.push_back(Action(SHOOT, enemy.agent_id));
            }
        }

        // Generate THROW actions (splash bombs) - max range 4 from source code
        if (agent.canThrow()) {
            for (int dx = -THROW_DISTANCE_MAX; dx <= THROW_DISTANCE_MAX; dx++) {
                for (int dy = -THROW_DISTANCE_MAX; dy <= THROW_DISTANCE_MAX; dy++) {
                    if (abs(dx) + abs(dy) <= THROW_DISTANCE_MAX) {
                        Coord target(agent.position.x + dx, agent.position.y + dy);
                        // Throw targets just need to be valid positions (can throw on occupied tiles)
                        if (game_state.isValidPosition(target)) {
                            possible_actions.push_back(Action(THROW, target));
                        }
                    }
                }
            }
        }

        // Always include HUNKER_DOWN as an option
        possible_actions.push_back(Action(HUNKER_DOWN));

        return possible_actions;
    }

    double scoreAction(const Action& action, const Agent& agent, const vector<Agent>& enemies) {
        double score = 0.0;

        switch (action.type) {
            case SHOOT:
                // Score shooting based on actual damage calculation
                for (const Agent& enemy : enemies) {
                    if (enemy.agent_id == action.target_id) {
                        int damage = game_state.calculateDamage(agent, enemy);
                        if (damage > 0) {
                            // Base score from damage
                            score += damage * 2;

                            // Bonus for potentially killing the enemy
                            if (enemy.wetness + damage >= DEATH_THRESHOLD) {
                                score += 200; // High priority for kills
                            }

                            // Bonus for high-value targets (low health enemies)
                            score += (DEATH_THRESHOLD - enemy.wetness) * 0.5;
                        }
                        break;
                    }
                }
                break;

            case MOVE:
                // Score movement based on strategic positioning
                if (!enemies.empty()) {
                    // Find closest enemy
                    int min_distance = INT_MAX;
                    for (const Agent& enemy : enemies) {
                        int distance = action.target.manhattanDistance(enemy.position);
                        min_distance = min(min_distance, distance);
                    }

                    // Prefer getting closer to enemies (but not too close)
                    if (min_distance > 1 && min_distance < 5) {
                        score += 50 - min_distance * 5;
                    }

                    // Bonus for moving to cover
                    int tile = game_state.getTile(action.target.x, action.target.y);
                    if (tile == TYPE_LOW_COVER) {
                        score += 20;
                    } else if (tile == TYPE_HIGH_COVER) {
                        score += 40; // High cover is better
                    }
                }
                break;

            case THROW:
                // Score throwing based on actual splash damage (3x3 area, 30 damage)
                if (agent.splash_bombs > 0) {
                    int enemiesHit = 0;
                    int totalDamageValue = 0;

                    for (const Agent& enemy : enemies) {
                        // Check if enemy is in 3x3 splash area (center + 8 adjacent)
                        int dx = abs(enemy.position.x - action.target.x);
                        int dy = abs(enemy.position.y - action.target.y);
                        if (dx <= 1 && dy <= 1) {
                            enemiesHit++;
                            totalDamageValue += THROW_DAMAGE;

                            // Bonus for potentially killing
                            if (enemy.wetness + THROW_DAMAGE >= DEATH_THRESHOLD) {
                                totalDamageValue += 100;
                            }
                        }
                    }

                    score += totalDamageValue * 1.5; // Splash damage is valuable
                    if (enemiesHit >= 2) {
                        score += 50; // Bonus for multi-target hits
                    }
                }
                break;

            case HUNKER_DOWN:
                // Low base score, but useful when no better options
                score = 10;
                break;

            default:
                score = 0;
        }

        return score;
    }

    Action chooseAction(const Agent& agent, const vector<Agent>& enemies) {
        if (use_mcts) {
            // Use MCTS for more sophisticated decision making
            Action mcts_action = mcts.search(game_state, agent.agent_id, 500);
            cerr << "Agent " << agent.agent_id << " chose MCTS action: " << mcts_action.toString() << endl;
            return mcts_action;
        } else {
            // Use heuristic-based approach
            vector<Action> possible_actions = generateAllPossibleActions(agent, enemies);

            if (possible_actions.empty()) {
                return Action(HUNKER_DOWN);
            }

            // Score all actions and pick the best one
            Action best_action = possible_actions[0];
            double best_score = scoreAction(best_action, agent, enemies);

            for (const Action& action : possible_actions) {
                double score = scoreAction(action, agent, enemies);
                if (score > best_score) {
                    best_score = score;
                    best_action = action;
                }
            }

            cerr << "Agent " << agent.agent_id << " chose heuristic action with score " << best_score << endl;
            return best_action;
        }
    }

    void outputActions(const vector<Action>& actions) {
        vector<Agent> my_agents = game_state.getMyAgents();

        // Output exactly the number of actions the game expects
        for (int i = 0; i < game_state.current_my_agent_count && i < actions.size() && i < my_agents.size(); i++) {
            cout << my_agents[i].agent_id << ";" << actions[i].toString() << endl;
            cout.flush(); // Ensure output is sent immediately
        }
    }

    void run() {
        parseInitialInput();

        // Game loop
        while (true) {
            parseGameTurn();
            vector<Action> actions = generateActions();
            outputActions(actions);
        }
    }
};

int main(int argc, char* argv[]) {
    bool use_mcts = true; // Default to MCTS

    // Check command line arguments
    if (argc > 1) {
        string mode = argv[1];
        if (mode == "heuristic" || mode == "h") {
            use_mcts = false;
            cerr << "Running in heuristic mode" << endl;
        } else if (mode == "mcts" || mode == "m") {
            use_mcts = true;
            cerr << "Running in MCTS mode" << endl;
        }
    } else {
        cerr << "Running in MCTS mode (default)" << endl;
    }

    SoakOverflowBot bot(use_mcts);
    bot.run();
    return 0;
}